import type { Metadata } from "next"
import { Outfit, Oxanium, JetBrains_Mono } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/providers/theme-provider"
import { Toaster } from "@/components/ui/sonner"
import SessionProvider from "@/components/providers/session-provider"
import NextTopLoader from 'nextjs-toploader'

const outfit = Outfit({ subsets: ["latin"], variable: "--font-outfit" })
const oxanium = Oxanium({ subsets: ["latin"], variable: "--font-oxanium" })
const jetBrainsMono = JetBrains_Mono({ subsets: ["latin"], variable: "--font-jetbrains-mono" })

// Top Loader Configuration
const topLoaderConfig = {
  color: "#8b5cf6",
  initialPosition: 0.08,
  crawlSpeed: 200,
  height: 3,
  crawl: true,
  showSpinner: true,
  easing: "ease",
  speed: 200,
  shadow: "0 0 10px #8b5cf6,0 0 5px #8b5cf6",
  zIndex: 1600,
}

export const metadata: Metadata = {
  title: "HVPPY Central - Where Creators and Fans Connect",
  description: "The ultimate social platform for creators and fans, powered by emotional AI and mood-based content discovery.",
  keywords: ["social media", "creators", "fans", "mood", "AI", "content discovery", "TikTok", "vertical feed"],
  authors: [{ name: "HVPPY Team" }],
  creator: "HVPPY",
  publisher: "HVPPY",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),
  openGraph: {
    title: "HVPPY Central - Where Creators and Fans Connect",
    description: "The ultimate social platform for creators and fans, powered by emotional AI and mood-based content discovery.",
    url: "/",
    siteName: "HVPPY Central",
    images: [
      {
        url: "/og-image.png",
        width: 1200,
        height: 630,
        alt: "HVPPY Central",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "HVPPY Central - Where Creators and Fans Connect",
    description: "The ultimate social platform for creators and fans, powered by emotional AI and mood-based content discovery.",
    images: ["/og-image.png"],
    creator: "@hvppy",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {


  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/icon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#8b5cf6" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
      </head>
      <body className={`${outfit.variable} ${oxanium.variable} ${jetBrainsMono.variable} font-sans antialiased`}>
        <NextTopLoader {...topLoaderConfig} />
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <SessionProvider>
            <div className="relative flex min-h-screen flex-col">
              <main className="flex-1">
                {children}
              </main>
            </div>
          </SessionProvider>
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: 'hsl(var(--background))',
                color: 'hsl(var(--foreground))',
                border: '1px solid hsl(var(--border))',
              },
            }}
          />
        </ThemeProvider>
      </body>
    </html>
  )
}
